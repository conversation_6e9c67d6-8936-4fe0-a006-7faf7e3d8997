# 📄 Product Requirements Document (PRD)

## 📘 Project Title

**Professional CV Website (Nuxt 3 + Nuxt Content + TypeScript)**

---

## 📍 Executive Summary

Build a modern, responsive, professional CV website using **Nuxt 3** with **Nuxt Content**, leveraging only Nuxt/Vue ecosystem-native dependencies and written entirely in **TypeScript**. The site will serve as a comprehensive digital portfolio presenting CV data, projects, and professional content in an SEO-optimized, accessible format deployable to modern hosting platforms.

### Key Success Metrics

- Lighthouse performance score ≥ 95
- Mobile-first responsive design
- Sub-2 second load times
- Full TypeScript coverage with zero `any` types
- WCAG 2.1 AA accessibility compliance

---

## 🔖 Version & Scope

**v1.0.0** - Initial MVP Release

**Scope**: Static site generation with content management, no authentication or dynamic server features required.

---

## 👥 Stakeholders & Responsibilities

|Role|Responsibility|Success Criteria|
|---|---|---|
|**Product Owner**|Define content requirements, approve design|Content accuracy, brand alignment|
|**AI Agent (Developer)**|Full implementation, testing, deployment|Technical requirements met, code quality|
|**End Users**|Consume content, evaluate candidacy|Positive user experience, easy navigation|

---

## 🎯 User Stories & Acceptance Criteria

### Epic 1: Core Navigation & Content Discovery

**US-001: As a recruiter, I want to quickly understand the candidate's profile**

- **AC1**: Landing page loads in <2s with hero section showing name, title, and key value proposition
- **AC2**: Navigation menu is visible and functional on all devices
- **AC3**: Call-to-action buttons are prominent and lead to relevant sections

**US-002: As a hiring manager, I want to access structured CV information**

- **AC1**: CV page displays chronological work experience with expandable details
- **AC2**: Skills are categorized and visually organized
- **AC3**: PDF download option is available and functional

**US-003: As a potential collaborator, I want to see project demonstrations**

- **AC1**: Projects page shows portfolio with filtering capabilities
- **AC2**: Each project includes live demo links, source code, and technology stack
- **AC3**: Project descriptions clearly explain problem solved and approach taken

### Epic 2: Content Management & SEO

**US-004: As a content author, I want to easily update CV information**

- **AC1**: All content is managed through markdown files with clear frontmatter structure
- **AC2**: Changes to content files automatically rebuild the site
- **AC3**: Content validation prevents broken builds

**US-005: As a search engine, I want to properly index the site content**

- **AC1**: All pages have proper meta tags, structured data, and Open Graph tags
- **AC2**: Sitemap is automatically generated and accessible
- **AC3**: Page titles and descriptions are unique and descriptive

---

## 🧩 Detailed Functional Requirements

### 1. Landing Page (`/`)

**Purpose**: First impression and quick navigation hub

**Content Requirements**:

- Hero section with professional headshot, name, current role/title
- Elevator pitch (2-3 sentences max)
- Key metrics or achievements (optional)
- Primary CTA: "View CV" and secondary: "See Projects"
- Skills preview (top 6-8 skills as badges)
- Recent blog posts preview (if blog enabled)

**Technical Requirements**:

- Hero image optimization with WebP format and lazy loading
- Smooth scroll to anchored sections
- Animated skill badges on scroll/hover
- Social proof section (testimonials, company logos)

### 2. About Page (`/about`)

**Purpose**: Comprehensive professional background

**Content Structure**:

```markdown
---
title: "About Me"
description: "Professional background and expertise"
keywords: ["software engineer", "full-stack", "typescript"]
---

# Professional Summary
[Detailed background paragraph]

## Core Competencies
### Technical Skills
- **Languages**: TypeScript, JavaScript, Python
- **Frameworks**: Vue.js, Nuxt.js, React
- **Tools**: Docker, Git, AWS

### Soft Skills
- Team Leadership
- Project Management
- Technical Communication

## Education & Certifications
[Structured list with dates, institutions, achievements]

## Philosophy & Approach
[Personal work philosophy and methodology]
```

**Technical Requirements**:

- Skills visualization with proficiency indicators
- Responsive timeline component for education/certifications
- Smooth animations for skill progress bars
- Download CV button with analytics tracking

### 3. CV/Resume Page (`/cv`)

**Purpose**: Detailed professional timeline

**Content Structure**:

```markdown
---
title: "Curriculum Vitae"
lastUpdated: "2024-12-15"
pdfUrl: "/cv-john-doe.pdf"
---

## Professional Experience

### Senior Full-Stack Developer | TechCorp Inc.
**Duration**: Jan 2022 - Present
**Location**: Remote
**Technologies**: Vue.js, Node.js, PostgreSQL, AWS

#### Key Responsibilities
- Led development team of 5 engineers
- Architected microservices platform handling 1M+ requests/day
- Implemented CI/CD pipeline reducing deployment time by 70%

#### Notable Achievements
- Increased application performance by 40%
- Mentored 3 junior developers to mid-level positions
```

**Technical Requirements**:

- Expandable/collapsible experience entries
- Technology tag filtering system
- Print-friendly CSS with proper page breaks
- PDF generation capability using Puppeteer or similar
- JSON-LD structured data for rich snippets

### 4. Projects Portfolio (`/projects`)

**Purpose**: Showcase technical capabilities and problem-solving

**Content Structure per Project**:

```markdown
---
title: "E-commerce Platform"
category: "Full-Stack Development"
technologies: ["Vue.js", "Express.js", "MongoDB", "Stripe API"]
status: "Production"
featured: true
demoUrl: "https://demo.example.com"
githubUrl: "https://github.com/user/project"
startDate: "2023-03-01"
endDate: "2023-08-15"
image: "/projects/ecommerce-preview.jpg"
---

## Project Overview
Built a complete e-commerce solution for small businesses with inventory management, payment processing, and analytics dashboard.

## Technical Challenges
- Real-time inventory synchronization
- Payment gateway integration
- Performance optimization for mobile

## Solutions Implemented
- WebSocket connections for real-time updates
- Caching strategy with Redis
- Progressive Web App features

## Results & Impact
- 99.9% uptime achieved
- 15% increase in client sales
- Featured in Vue.js community showcase
```

**Technical Requirements**:

- Filter by technology, category, or status
- Image gallery with lightbox functionality
- Live demo embedding (iframe with fallback)
- GitHub integration showing commit activity
- Project search functionality

### 5. Contact Page (`/contact`)

**Purpose**: Professional communication channel

**Form Requirements**:

```typescript
interface ContactForm {
  name: string; // Required, min 2 chars
  email: string; // Required, valid email format
  company?: string; // Optional
  subject: string; // Required, dropdown options
  message: string; // Required, min 50 chars, max 1000
}
```

**Validation Rules**:

- Client-side validation with immediate feedback
- Server-side validation with sanitization
- Rate limiting: 5 submissions per hour per IP
- Honeypot field for spam prevention
- reCAPTCHA integration for production

**Form Handling Options**:

1. **FormKit** (recommended for static deployment)
2. **Custom Nuxt server route** with email service

### 6. Blog Section (`/blog`) - Optional

**Content Management**:

```markdown
---
title: "Modern Vue.js Development Patterns"
description: "Exploring composition API and best practices"
publishDate: "2024-01-15"
updateDate: "2024-01-20"
category: "Technical"
tags: ["Vue.js", "TypeScript", "Best Practices"]
author: "John Doe"
featured: true
readingTime: "8 min"
---

# Article Content Here
```

**Features Required**:

- Article listing with pagination (10 posts per page)
- Category and tag filtering
- Reading time estimation
- Social sharing buttons
- RSS feed generation
- Search functionality using Fuse.js

---

## 🧱 Technical Architecture & Requirements

### Core Technology Stack

|Component|Technology|Version|Justification|
|---|---|---|---|
|**Framework**|Nuxt 3|^3.8.0|Vue 3 + SSG + SEO optimization|
|**Language**|TypeScript|^5.0.0|Type safety + better DX|
|**Content**|@nuxt/content|^2.8.0|Git-based CMS + markdown|
|**Styling**|@nuxt/tailwindcss|^6.8.0|utility-first CSS|
|**Icons**|@nuxt/icon|^1.0.0|Iconify integration|
|**SEO**|@nuxtjs/seo|^2.0.0|Meta tags + structured data|
|**Analytics**|@nuxtjs/google-analytics|^3.0.0|Optional user tracking|
|**Component Library**|Nuxt UI|^3.0.0|Tailwind + Vue components|

### Performance Requirements

|Metric|Target|Measurement Method|
|---|---|---|
|**First Contentful Paint**|< 1.5s|Lighthouse CI|
|**Largest Contentful Paint**|< 2.5s|Core Web Vitals|
|**Cumulative Layout Shift**|< 0.1|Real User Monitoring|
|**Time to Interactive**|< 3.0s|PageSpeed Insights|
|**Bundle Size**|< 250KB gzipped|Webpack Bundle Analyzer|

### TypeScript Configuration

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  typescript: {
    strict: true,
    typeCheck: true
  }
})

// types/index.ts
export interface CVExperience {
  id: string
  title: string
  company: string
  location: string
  startDate: string
  endDate?: string
  current: boolean
  description: string
  technologies: string[]
  achievements: string[]
}

export interface Project {
  slug: string
  title: string
  description: string
  longDescription: string
  technologies: Technology[]
  category: ProjectCategory
  status: ProjectStatus
  featured: boolean
  demoUrl?: string
  githubUrl?: string
  images: ProjectImage[]
  startDate: string
  endDate?: string
}

export type ProjectStatus = 'planning' | 'development' | 'production' | 'archived'
export type ProjectCategory = 'web-app' | 'mobile-app' | 'api' | 'library' | 'tool'
```

### Content Schema Validation

```typescript
// composables/useContentValidation.ts
export const useContentValidation = () => {
  const validateProject = (project: any): project is Project => {
    return (
      typeof project.title === 'string' &&
      typeof project.description === 'string' &&
      Array.isArray(project.technologies) &&
      ['planning', 'development', 'production', 'archived'].includes(project.status)
    )
  }
  
  return { validateProject }
}
```

---

## 🏗️ Implementation Phases & Detailed Tasks

### Phase 1: Project Foundation (2-3 days)

**Task 1.1: Project Initialization**

```bash
# Commands for AI agent to execute:
npx nuxi@latest init cv-website --packageManager pnpm
cd cv-website
pnpm install

# Install core dependencies
pnpm add -D @nuxt/content @nuxt/tailwindcss @nuxt/icon @nuxtjs/seo typescript vue-tsc

# Setup TypeScript configuration
touch types/index.ts composables/.gitkeep
```

**Task 1.2: Nuxt Configuration**

```typescript
// nuxt.config.ts template for AI agent
export default defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
    '@nuxt/content',
    '@nuxt/tailwindcss', 
    '@nuxt/icon',
    '@nuxtjs/seo'
  ],
  content: {
    highlight: {
      theme: 'github-dark'
    },
    markdown: {
      toc: { depth: 3 }
    }
  },
  tailwindcss: {
    cssPath: '~/assets/css/main.css'
  },
  seo: {
    redirectToCanonicalSiteUrl: true
  },
  typescript: {
    strict: true,
    typeCheck: true
  }
})
```

**Task 1.3: Base Styling Setup**

```css
/* assets/css/main.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-gray-900 bg-white;
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200;
  }
  
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8 py-16;
  }
}
```

### Phase 2: Core Components (3-4 days)

**Task 2.1: Layout Components**

_TheHeader.vue_:

```vue
<template>
  <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <NuxtLink to="/" class="font-bold text-xl text-gray-900">
          {{ siteConfig.name }}
        </NuxtLink>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:flex space-x-8">
          <NuxtLink
            v-for="item in navigation"
            :key="item.path"
            :to="item.path"
            class="text-gray-600 hover:text-gray-900 transition-colors"
            :class="{ 'text-blue-600 font-medium': $route.path === item.path }"
          >
            {{ item.name }}
          </NuxtLink>
        </div>
        
        <!-- Mobile Menu Button -->
        <button 
          @click="toggleMobile"
          class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900"
        >
          <Icon :name="mobileOpen ? 'heroicons:x-mark' : 'heroicons:bars-3'" class="w-6 h-6" />
        </button>
      </div>
      
      <!-- Mobile Navigation -->
      <Transition
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="opacity-0 scale-95"
        enter-to-class="opacity-100 scale-100"
      >
        <div v-show="mobileOpen" class="md:hidden py-4">
          <!-- Mobile nav items -->
        </div>
      </Transition>
    </nav>
  </header>
</template>

<script setup lang="ts">
interface NavItem {
  name: string
  path: string
}

const siteConfig = {
  name: 'John Doe'
}

const navigation: NavItem[] = [
  { name: 'About', path: '/about' },
  { name: 'CV', path: '/cv' },
  { name: 'Projects', path: '/projects' },
  { name: 'Contact', path: '/contact' }
]

const mobileOpen = ref(false)
const toggleMobile = () => mobileOpen.value = !mobileOpen.value
</script>
```

**Task 2.2: Content Components**

_ProjectCard.vue_:

```vue
<template>
  <article class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
    <div v-if="project.image" class="aspect-video bg-gray-100">
      <img 
        :src="project.image" 
        :alt="project.title"
        class="w-full h-full object-cover"
        loading="lazy"
      />
    </div>
    
    <div class="p-6">
      <div class="flex items-center justify-between mb-3">
        <span class="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
          {{ project.category }}
        </span>
        <ProjectStatus :status="project.status" />
      </div>
      
      <h3 class="text-xl font-semibold text-gray-900 mb-2">
        {{ project.title }}
      </h3>
      
      <p class="text-gray-600 mb-4 line-clamp-3">
        {{ project.description }}
      </p>
      
      <div class="flex flex-wrap gap-2 mb-4">
        <TechBadge 
          v-for="tech in project.technologies.slice(0, 4)" 
          :key="tech"
          :technology="tech"
        />
        <span v-if="project.technologies.length > 4" class="text-sm text-gray-500">
          +{{ project.technologies.length - 4 }} more
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <div class="flex space-x-3">
          <a 
            v-if="project.demoUrl"
            :href="project.demoUrl"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 font-medium text-sm"
          >
            Live Demo ↗
          </a>
          <a 
            v-if="project.githubUrl"
            :href="project.githubUrl"
            target="_blank"
            rel="noopener noreferrer"
            class="text-gray-600 hover:text-gray-800 font-medium text-sm"
          >
            Code ↗
          </a>
        </div>
        
        <NuxtLink 
          :to="`/projects/${project.slug}`"
          class="text-blue-600 hover:text-blue-800 font-medium text-sm"
        >
          Read More →
        </NuxtLink>
      </div>
    </div>
  </article>
</template>

<script setup lang="ts">
interface Props {
  project: Project
}

defineProps<Props>()
</script>
```

### Phase 3: Page Implementation (3-4 days)

**Task 3.1: Dynamic Content Pages**

_pages/projects/[slug].vue_:

```vue
<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Breadcrumb Navigation -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          <li><NuxtLink to="/" class="hover:text-gray-900">Home</NuxtLink></li>
          <li>→</li>
          <li><NuxtLink to="/projects" class="hover:text-gray-900">Projects</NuxtLink></li>
          <li>→</li>
          <li class="text-gray-900">{{ project?.title }}</li>
        </ol>
      </nav>
      
      <!-- Project Header -->
      <header class="mb-12">
        <div class="flex items-center gap-4 mb-4">
          <h1 class="text-4xl font-bold text-gray-900">
            {{ project?.title }}
          </h1>
          <ProjectStatus :status="project?.status" />
        </div>
        
        <p class="text-xl text-gray-600 mb-6">
          {{ project?.description }}
        </p>
        
        <!-- Project Meta -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 bg-white rounded-lg border border-gray-200">
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Duration</h3>
            <p class="text-gray-600">
              {{ formatDateRange(project?.startDate, project?.endDate) }}
            </p>
          </div>
          
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Category</h3>
            <p class="text-gray-600">{{ project?.category }}</p>
          </div>
          
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Links</h3>
            <div class="flex space-x-4">
              <a 
                v-if="project?.demoUrl"
                :href="project.demoUrl"
                target="_blank"
                class="text-blue-600 hover:text-blue-800"
              >
                Live Demo ↗
              </a>
              <a 
                v-if="project?.githubUrl"
                :href="project.githubUrl"
                target="_blank"
                class="text-gray-600 hover:text-gray-800"
              >
                GitHub ↗
              </a>
            </div>
          </div>
        </div>
      </header>
      
      <!-- Project Content -->
      <div class="prose prose-lg max-w-none">
        <ContentRenderer :value="project" />
      </div>
      
      <!-- Technologies Used -->
      <section class="mt-12 p-6 bg-white rounded-lg border border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Technologies Used</h2>
        <div class="flex flex-wrap gap-3">
          <TechBadge 
            v-for="tech in project?.technologies" 
            :key="tech"
            :technology="tech"
            size="lg"
          />
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { data: project } = await useAsyncData(`project-${route.params.slug}`, () => 
  queryContent('/projects').where({ slug: route.params.slug }).findOne()
)

if (!project.value) {
  throw createError({ statusCode: 404, statusMessage: 'Project not found' })
}

// SEO
useSeoMeta({
  title: project.value.title,
  description: project.value.description,
  ogTitle: project.value.title,
  ogDescription: project.value.description,
  ogImage: project.value.image,
})

const formatDateRange = (start?: string, end?: string) => {
  if (!start) return 'Unknown'
  const startDate = new Date(start).toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
  const endDate = end ? new Date(end).toLocaleDateString('en-US', { year: 'numeric', month: 'long' }) : 'Present'
  return `${startDate} - ${endDate}`
}
</script>
```

### Phase 4: Content Management (2 days)

**Task 4.1: Content Structure Setup**

Create directory structure and sample content:

```
/content
├── about.md
├── experience/
│   ├── senior-developer-techcorp.md
│   ├── full-stack-developer-startup.md
│   └── junior-developer-agency.md
├── projects/
│   ├── ecommerce-platform.md
│   ├── task-management-app.md
│   └── open-source-library.md
├── blog/
│   ├── vue-composition-api-patterns.md
│   ├── typescript-best-practices.md
│   └── nuxt-performance-optimization.md
└── config/
    ├── site.yml
    ├── navigation.yml
    └── social.yml
```

**Task 4.2: Content Templates**

_content/experience/senior-developer-techcorp.md_:

```markdown
---
title: "Senior Full-Stack Developer"
company: "TechCorp Inc."
location: "San Francisco, CA (Remote)"
startDate: "2022-01-15"
endDate: null
current: true
type: "Full-time"
technologies: 
  - "Vue.js"
  - "Nuxt.js"
  - "Node.js"
  - "PostgreSQL"
  - "AWS"
  - "Docker"
  - "TypeScript"
category: "Software Development"
salary: "$120,000 - $140,000"
order: 1
---

## Role Overview

Lead a cross-functional team of 5 engineers in developing and maintaining a high-traffic SaaS platform serving over 50,000 active users. Responsible for architectural decisions, code reviews, and mentoring junior developers.

## Key Responsibilities

- **Technical Leadership**: Designed and implemented microservices architecture handling 1M+ requests daily
- **Team Management**: Mentored 3 junior developers, conducted code reviews, and established development best practices
- **Product Development**: Collaborated with product managers to deliver features that increased user engagement by 35%
- **Infrastructure**: Set up CI/CD pipelines that reduced deployment time from 2 hours to 15 minutes
- **Performance Optimization**: Improved application performance by 40% through database optimization and caching strategies

## Notable Projects

### User Analytics Dashboard
Built a real-time analytics dashboard using Vue.js and D3.js that processes millions of data points. Implemented efficient data visualization techniques that improved loading times by 60%.

### API Gateway Migration
Led the migration from monolithic architecture to microservices, resulting in improved scalability and reduced downtime from 2 hours/month to 15 minutes/month.

## Achievements

- **Performance**: Reduced application bundle size by 45% through code splitting and lazy loading
- **Quality**: Maintained 95%+ test coverage across all frontend components
- **Leadership**: Successfully onboarded 8 new team members with zero production incidents
- **Innovation**: Introduced TypeScript adoption, reducing runtime errors by 70%

## Skills Developed

- Advanced Vue.js patterns and composition API
- Microservices architecture and API design
- Database optimization and query performance
- Team leadership and cross-functional collaboration
- Agile development methodologies
```

### Phase 5: SEO & Performance (1-2 days)

**Task 5.1: SEO Implementation**

_composables/useSEO.ts_:

```typescript
export const useSEO = () => {
  const setSEO = (options: {
    title: string
    description: string
    image?: string
    url?: string
    type?: string
    keywords?: string[]
  }) => {
    const route = useRoute()
    const config = useRuntimeConfig()
    
    const fullUrl = `${config.public.siteUrl}${route.path}`
    const imageUrl = options.image ? `${config.public.siteUrl}${options.image}` : `${config.public.siteUrl}/og-default.jpg`
    
    useSeoMeta({
      title: `${options.title} | John Doe - Full-Stack Developer`,
      description: options.description,
      keywords: options.keywords?.join(', '),
      
      // Open Graph
      ogTitle: options.title,
      ogDescription: options.description,
      ogImage: imageUrl,
      ogUrl: fullUrl,
      ogType: options.type || 'website',
      
      // Twitter
      twitterCard: 'summary_large_image',
      twitterTitle: options.title,
      twitterDescription: options.description,
      twitterImage: imageUrl,
      
      // Additional
      canonical: fullUrl
    })
    
    // Structured Data
    useJsonld({
      '@context': 'https://schema.org',
      '@type': 'Person',
      name: 'John Doe',
      jobTitle: 'Senior Full-Stack Developer',
      url: config.public.siteUrl,
      sameAs: [
        'https://github.com/johndoe',
        'https://linkedin.com/in/johndoe',
        'https://twitter.com/johndoe'
      ]
    })
  }
  
  return { setSEO }
}
```

**Task 5.2: Performance Optimization**

_nuxt.config.ts additions_:

```typescript
export default defineNuxtConfig({
  // ... existing config
  
  experimental: {
    payloadExtraction: false
  },
  
  nitro: {
    prerender: {
      routes: ['/sitemap.xml', '/robots.txt']
    },
    compressPublicAssets: true
  },
  
  image: {
    formats: ['webp', 'avif', 'jpg'],
    quality: 80,
    densities: [1, 2]
  },
  
  css: ['~/assets/css/main.css'],
  
  hooks: {
    'build:before': () => {
      console.log('Optimizing assets...')
    }
  }
})
```

---

## 🧪 Testing Strategy & Quality Assurance

### Testing Requirements

|Test Type|Coverage Target|Tools|Automation|
|---|---|---|---|
|**Unit Tests**|80%+|Vitest|CI/CD|
|**Component Tests**|70%+|Vue Test Utils|CI/CD|
|**E2E Tests**|Critical paths|Playwright|CI/CD|
|**Accessibility**|WCAG 2.1 AA|axe-core|Manual + CI|
|**Performance**|Lighthouse 95+|Lighthouse CI|CI/CD|
|**SEO**|All pages|Custom scripts|CI/CD|

### Test Implementation

_tests/components/ProjectCard.spec.ts_:

```typescript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import ProjectCard from '~/components/ProjectCard.vue'
import type { Project } from '~/types'

const mockProject: Project = {
  slug: 'test-project',
  title: 'Test Project',
  description: 'A test project for unit testing',
  technologies: ['Vue.js', 'TypeScript'],
  category: 'web-app',
  status: 'production',
  featured: true,
  startDate: '2023-01-01',
  image: '/test-image.jpg'
}

describe('ProjectCard', () => {
  it('renders project information correctly', () => {
    const wrapper = mount(ProjectCard, {
      props: { project: mockProject }
    })
    
    expect(wrapper.find('h3').text()).toBe(mockProject.title)
    expect(wrapper.find('p').text()).toBe(mockProject.description)
    expect(wrapper.findAll('[data-testid="tech-badge"]')).toHaveLength(2)
  })
  
  it('shows correct status badge', () => {
    const wrapper = mount(ProjectCard, {
      props: { project: mockProject }
    })
    
    expect(wrapper.find('[data-testid="project-status"]').text()).toContain('Production')
  })
  
  it('handles missing optional props gracefully', () => {
    const minimalProject = {
      ...mockProject,
      demoUrl: undefined,
      githubUrl: undefined,
      image: undefined
    }
    
    const wrapper = mount(ProjectCard, {
      props: { project: minimalProject }
    })
    
    expect(wrapper.find('[data-testid="demo-link"]').exists()).toBe(false)
    expect(wrapper.find('[data-testid="github-link"]').exists()).toBe(false)
    expect(wrapper.find('img').exists()).toBe(false)
  })
  
  it('emits events correctly', async () => {
    const wrapper = mount(ProjectCard, {
      props: { project: mockProject }
    })
    
    await wrapper.find('[data-testid="read-more"]').trigger('click')
    expect(wrapper.emitted('project-selected')).toBeTruthy()
  })
})
```

_tests/e2e/navigation.spec.ts_:

```typescript
import { test, expect } from '@playwright/test'

test.describe('Site Navigation', () => {
  test('should navigate through all main pages', async ({ page }) => {
    await page.goto('/')
    
    // Test home page
    await expect(page.locator('h1')).toContainText('John Doe')
    await expect(page.locator('[data-testid="hero-cta"]')).toBeVisible()
    
    // Navigate to About
    await page.click('nav a[href="/about"]')
    await expect(page.locator('h1')).toContainText('About')
    await expect(page.locator('[data-testid="skills-section"]')).toBeVisible()
    
    // Navigate to CV
    await page.click('nav a[href="/cv"]')
    await expect(page.locator('h1')).toContainText('Curriculum Vitae')
    await expect(page.locator('[data-testid="experience-timeline"]')).toBeVisible()
    
    // Navigate to Projects
    await page.click('nav a[href="/projects"]')
    await expect(page.locator('h1')).toContainText('Projects')
    await expect(page.locator('[data-testid="project-grid"]')).toBeVisible()
    
    // Navigate to Contact
    await page.click('nav a[href="/contact"]')
    await expect(page.locator('h1')).toContainText('Contact')
    await expect(page.locator('form[data-testid="contact-form"]')).toBeVisible()
  })
  
  test('should have working mobile navigation', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Mobile menu should be closed initially
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeHidden()
    
    // Click mobile menu button
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible()
    
    // Navigate using mobile menu
    await page.click('[data-testid="mobile-nav"] a[href="/about"]')
    await expect(page.url()).toContain('/about')
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeHidden()
  })
  
  test('should handle 404 pages gracefully', async ({ page }) => {
    await page.goto('/non-existent-page')
    await expect(page.locator('h1')).toContainText('Page Not Found')
    await expect(page.locator('[data-testid="back-home-link"]')).toBeVisible()
  })
})

test.describe('Performance', () => {
  test('should meet performance benchmarks', async ({ page }) => {
    await page.goto('/')
    
    // Measure load time
    const loadTime = await page.evaluate(() => performance.timing.loadEventEnd - performance.timing.navigationStart)
    expect(loadTime).toBeLessThan(3000) // 3 seconds max
    
    // Check for lazy loading
    await expect(page.locator('img[loading="lazy"]')).toHaveCount(0, { timeout: 1000 })
  })
})
```

### Accessibility Testing

_tests/accessibility/a11y.spec.ts_:

```typescript
import { test, expect } from '@playwright/test'
import AxeBuilder from '@axe-core/playwright'

test.describe('Accessibility', () => {
  test('should pass axe accessibility tests on all pages', async ({ page }) => {
    const pages = ['/', '/about', '/cv', '/projects', '/contact']
    
    for (const pagePath of pages) {
      await page.goto(pagePath)
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze()
        
      expect(accessibilityScanResults.violations).toEqual([])
    }
  })
  
  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/')
    
    // Tab through navigation
    await page.keyboard.press('Tab')
    await expect(page.locator('nav a:first-child')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('nav a:nth-child(2)')).toBeFocused()
    
    // Test skip link
    await page.keyboard.press('Tab')
    await page.keyboard.press('Enter')
    await expect(page.locator('main')).toBeFocused()
  })
  
  test('should have proper ARIA labels', async ({ page }) => {
    await page.goto('/')
    
    // Check for proper heading hierarchy
    const h1Count = await page.locator('h1').count()
    expect(h1Count).toBe(1)
    
    // Check navigation landmarks
    await expect(page.locator('nav[aria-label="Main navigation"]')).toBeVisible()
    await expect(page.locator('main[role="main"]')).toBeVisible()
    
    // Check form labels
    await page.goto('/contact')
    const formInputs = page.locator('input, textarea, select')
    const inputCount = await formInputs.count()
    
    for (let i = 0; i < inputCount; i++) {
      const input = formInputs.nth(i)
      const hasLabel = await input.evaluate(el => {
        const id = el.getAttribute('id')
        return id && document.querySelector(`label[for="${id}"]`)
      })
      expect(hasLabel).toBeTruthy()
    }
  })
})
```

---

## 🚀 Deployment & DevOps Strategy

### CI/CD Pipeline Configuration

_.github/workflows/deploy.yml_:

```yaml
name: Deploy CV Website

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    - name: Run linting
      run: pnpm run lint
      
    - name: Type checking
      run: pnpm run type-check
      
    - name: Run unit tests
      run: pnpm run test:unit
      
    - name: Run component tests
      run: pnpm run test:component
      
    - name: Install Playwright
      run: pnpm exec playwright install
      
    - name: Run E2E tests
      run: pnpm run test:e2e
      
    - name: Run accessibility tests
      run: pnpm run test:a11y
      
  lighthouse-audit:
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    - name: Build application
      run: pnpm run build
      
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        
  deploy-preview:
    runs-on: ubuntu-latest
    needs: [lint-and-test, lighthouse-audit]
    if: github.event_name == 'pull_request'
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    - name: Build for preview
      run: pnpm run build
      
    - name: Deploy to Netlify Preview
      uses: nwtgck/actions-netlify@v2.0
      with:
        publish-dir: './dist'
        github-token: ${{ secrets.GITHUB_TOKEN }}
        deploy-message: "Deploy from GitHub Actions"
        enable-pull-request-comment: true
        enable-commit-comment: false
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
  deploy-production:
    runs-on: ubuntu-latest
    needs: [lint-and-test, lighthouse-audit]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    - name: Build for production
      run: pnpm run build
      env:
        NUXT_PUBLIC_SITE_URL: https://johndoe.dev
        NUXT_PUBLIC_ANALYTICS_ID: ${{ secrets.ANALYTICS_ID }}
        
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        github-token: ${{ secrets.GITHUB_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
        working-directory: ./
```

### Environment Configuration

_.env.example_:

```bash
# Site Configuration
NUXT_PUBLIC_SITE_URL=https://johndoe.dev
NUXT_PUBLIC_SITE_NAME="John Doe - Full-Stack Developer"
NUXT_PUBLIC_SITE_DESCRIPTION="Senior Full-Stack Developer specializing in Vue.js, Node.js, and TypeScript"

# Analytics
NUXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NUXT_PUBLIC_HOTJAR_ID=XXXXXXX

# Contact Form
NUXT_CONTACT_EMAIL=<EMAIL>
NUXT_SMTP_HOST=smtp.gmail.com
NUXT_SMTP_PORT=587
NUXT_SMTP_USER=<EMAIL>
NUXT_SMTP_PASS=your-app-password

# Social Media
NUXT_PUBLIC_GITHUB_URL=https://github.com/johndoe
NUXT_PUBLIC_LINKEDIN_URL=https://linkedin.com/in/johndoe
NUXT_PUBLIC_TWITTER_URL=https://twitter.com/johndoe

# Security
NUXT_SECRET_KEY=your-secret-key-for-forms
NUXT_RECAPTCHA_SITE_KEY=your-recaptcha-site-key
NUXT_RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key
```

### Lighthouse CI Configuration

_lighthouserc.js_:

```javascript
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/about',
        'http://localhost:3000/cv',
        'http://localhost:3000/projects',
        'http://localhost:3000/contact'
      ],
      startServerCommand: 'pnpm run preview',
      startServerReadyPattern: 'Local:',
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['warn', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.95 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
}
```

---

## 📊 Analytics & Monitoring Strategy

### Performance Monitoring

_composables/useAnalytics.ts_:

```typescript
export const useAnalytics = () => {
  const config = useRuntimeConfig()
  
  const trackPageView = (path: string) => {
    if (typeof gtag !== 'undefined') {
      gtag('config', config.public.googleAnalyticsId, {
        page_path: path
      })
    }
  }
  
  const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        custom_map: { dimension1: 'cv_website' },
        ...parameters
      })
    }
  }
  
  const trackDownload = (fileName: string) => {
    trackEvent('file_download', {
      file_name: fileName,
      file_extension: fileName.split('.').pop()
    })
  }
  
  const trackContactFormSubmission = (success: boolean) => {
    trackEvent('contact_form_submit', {
      success,
      form_type: 'contact'
    })
  }
  
  const trackProjectView = (projectSlug: string) => {
    trackEvent('project_view', {
      project_slug: projectSlug
    })
  }
  
  return {
    trackPageView,
    trackEvent,
    trackDownload,
    trackContactFormSubmission,
    trackProjectView
  }
}
```

### Error Monitoring

_plugins/error-tracking.client.ts_:

```typescript
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.config.errorHandler = (error, context) => {
    console.error('Vue Error:', error, context)
    
    // Send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Integration with Sentry or similar
      const errorData = {
        message: error.message,
        stack: error.stack,
        context: context?.$options?.__file || 'unknown',
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
      
      // Send to error tracking service
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorData)
      }).catch(console.error)
    }
  }
  
  // Track unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason)
    
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'unhandled_promise_rejection',
          message: event.reason?.message || 'Unknown promise rejection',
          stack: event.reason?.stack,
          timestamp: new Date().toISOString()
        })
      }).catch(console.error)
    }
  })
})
```

---

## 🔧 Advanced Features & Optimizations

### Image Optimization System

_composables/useOptimizedImage.ts_:

```typescript
export const useOptimizedImage = () => {
  const generateSrcSet = (imagePath: string, sizes: number[] = [400, 800, 1200, 1600]) => {
    return sizes.map(size => 
      `/_nuxt/image/${size}/${imagePath.replace(/^\//, '')} ${size}w`
    ).join(', ')
  }
  
  const getOptimizedImageProps = (
    src: string, 
    alt: string, 
    options: {
      eager?: boolean
      sizes?: string
      aspectRatio?: string
    } = {}
  ) => {
    const { eager = false, sizes = '(max-width: 768px) 100vw, 50vw', aspectRatio } = options
    
    return {
      src,
      alt,
      loading: eager ? 'eager' : 'lazy',
      decoding: 'async',
      srcset: generateSrcSet(src),
      sizes,
      style: aspectRatio ? { aspectRatio } : undefined
    }
  }
  
  return { generateSrcSet, getOptimizedImageProps }
}
```

### Advanced Form Handling

_server/api/contact.post.ts_:

```typescript
import { z } from 'zod'
import nodemailer from 'nodemailer'
import rateLimit from 'express-rate-limit'

const contactSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  company: z.string().optional(),
  subject: z.enum(['general', 'collaboration', 'job-opportunity', 'other']),
  message: z.string().min(50).max(1000),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  honeypot: z.string().optional(), // Spam prevention
  recaptchaToken: z.string()
})

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many contact form submissions, please try again later.'
})

export default defineEventHandler(async (event) => {
  // Apply rate limiting
  await limiter(event.node.req, event.node.res, () => {})
  
  if (event.node.req.method !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    })
  }
  
  try {
    const body = await readBody(event)
    
    // Validate input
    const validatedData = contactSchema.parse(body)
    
    // Check honeypot (should be empty)
    if (validatedData.honeypot) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Spam detected'
      })
    }
    
    // Verify reCAPTCHA
    const recaptchaResponse = await $fetch(`https://www.google.com/recaptcha/api/siteverify`, {
      method: 'POST',
      body: new URLSearchParams({
        secret: useRuntimeConfig().recaptchaSecretKey,
        response: validatedData.recaptchaToken
      })
    })
    
    if (!recaptchaResponse.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'reCAPTCHA verification failed'
      })
    }
    
    // Send email
    const transporter = nodemailer.createTransporter({
      host: useRuntimeConfig().smtpHost,
      port: useRuntimeConfig().smtpPort,
      auth: {
        user: useRuntimeConfig().smtpUser,
        pass: useRuntimeConfig().smtpPass
      }
    })
    
    const emailContent = `
      New Contact Form Submission
      
      Name: ${validatedData.name}
      Email: ${validatedData.email}
      Company: ${validatedData.company || 'Not provided'}
      Subject: ${validatedData.subject}
      Budget: ${validatedData.budget || 'Not provided'}
      Timeline: ${validatedData.timeline || 'Not provided'}
      
      Message:
      ${validatedData.message}
    `
    
    await transporter.sendMail({
      from: useRuntimeConfig().smtpUser,
      to: useRuntimeConfig().contactEmail,
      subject: `CV Website Contact: ${validatedData.subject}`,
      text: emailContent,
      replyTo: validatedData.email
    })
    
    // Log submission (for analytics)
    console.log(`Contact form submission from ${validatedData.email}`)
    
    return {
      success: true,
      message: 'Thank you for your message. I\'ll get back to you soon!'
    }
    
  } catch (error) {
    if (error.issues) {
      // Zod validation error
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid form data',
        data: error.issues
      })
    }
    
    console.error('Contact form error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to send message'
    })
  }
})
```

### Content Search Implementation

_composables/useSearch.ts_:

```typescript
import Fuse from 'fuse.js'

export const useSearch = () => {
  const searchIndex = ref<Fuse<any> | null>(null)
  const searchResults = ref([])
  const isSearching = ref(false)
  
  const initializeSearch = async () => {
    const { data: allContent } = await $fetch('/api/search-index')
    
    const fuseOptions = {
      keys: [
        { name: 'title', weight: 0.4 },
        { name: 'description', weight: 0.3 },
        { name: 'body', weight: 0.2 },
        { name: 'technologies', weight: 0.1 }
      ],
      threshold: 0.3,
      includeScore: true,
      includeMatches: true
    }
    
    searchIndex.value = new Fuse(allContent, fuseOptions)
  }
  
  const search = async (query: string) => {
    if (!searchIndex.value || query.length < 2) {
      searchResults.value = []
      return
    }
    
    isSearching.value = true
    
    try {
      const results = searchIndex.value.search(query)
      searchResults.value = results.map(result => ({
        ...result.item,
        score: result.score,
        matches: result.matches
      }))
    } finally {
      isSearching.value = false
    }
  }
  
  const clearSearch = () => {
    searchResults.value = []
  }
  
  return {
    searchResults: readonly(searchResults),
    isSearching: readonly(isSearching),
    initializeSearch,
    search,
    clearSearch
  }
}
```

---

## 🎨 Design System & UI Components

### Design Tokens

_assets/css/design-tokens.css_:

```css
:root {
  /* Colors */
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-500: #6b7280;
  --color-gray-900: #111827;
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'JetBrains Mono', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark mode tokens */
@media (prefers-color-scheme: dark) {
  :root {
    --color-gray-50: #1f2937;
    --color-gray-100: #374151;
    --color-gray-500: #9ca3af;
    --color-gray-900: #f9fafb;
  }
}
```

### Component Library Structure

_components/ui/Button.vue_:

```vue
<template>
  <component
    :is="tag"
    :class="buttonClasses"
    :disabled="disabled || loading"
    v-bind="$attrs"
  >
    <Icon 
      v-if="loading" 
      name="heroicons:arrow-path" 
      class="animate-spin mr-2" 
      :class="iconSizeClass"
    />
    <Icon 
      v-else-if="icon" 
      :name="icon" 
      class="mr-2" 
      :class="iconSizeClass"
    />
    <slot />
  </component>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string
  tag?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  tag: 'button'
})

const buttonClasses = computed(() => {
  const base = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    ghost: 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  }
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl'
  }
  
  const disabled = props.disabled || props.loading ? 'opacity-50 cursor-not-allowed' : ''
  
  return [base, variants[props.variant], sizes[props.size], disabled].join(' ')
})

const iconSizeClass = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }
  return sizes[props.size]
})
</script>
```

---

## 📈 Success Metrics & KPIs

### Technical Metrics

|Metric|Target|Measurement|Frequency|
|---|---|---|---|
|**Lighthouse Performance**|95+|Lighthouse CI|Every deploy|
|**First Contentful Paint**|< 1.5s|Real User Monitoring|Daily|
|**Cumulative Layout Shift**|< 0.1|Core Web Vitals|Daily|
|**Time to Interactive**|< 3.0s|PageSpeed Insights|Weekly|
|**Bundle Size**|< 250KB|Webpack Bundle Analyzer|Every build|
|**Code Coverage**|80%+|Jest/Vitest|Every commit|
|**TypeScript Strict Mode**|100%|vue-tsc|Every commit|
|**Accessibility Score**|95+|axe-core testing|Every deploy|
